import 'dart:async';

import 'package:flutter/foundation.dart' show kDebugMode;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/features/ai_chat/ai_chat_bloc/ai_chat_bloc.dart';
import 'package:luxury_app/features/ai_chat/ai_chat_view/chat_layout.dart';
import 'package:luxury_app/features/ai_chat/ai_chat_view/message_input_widget.dart';
import 'package:luxury_app/features/ai_chat/data/ai_attachment.dart';
import 'package:luxury_app/features/settings/auth/auth_provider.dart';

/// Главный виджет экрана чата с ИИ с использованием Riverpod
class AIChatContent extends ConsumerStatefulWidget {
  final int chatId;

  const AIChatContent({super.key, required this.chatId});

  @override
  ConsumerState<AIChatContent> createState() => _AIChatContentState();
}

/// Состояние экрана чата с ИИ
class _AIChatContentState extends ConsumerState<AIChatContent>
    with WidgetsBindingObserver {
  Timer? _inactivityTimer;
  bool _isAppInBackground = false;
  late final ScrollController _scrollController;
  late final TextEditingController _textController;

  // Для отслеживания состояний
  bool _lastErrorShown = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _scrollController = ScrollController();
    _textController = TextEditingController();
    _resetInactivityTimer();

    if (kDebugMode) {
      debugPrint('🚀 [AIChatContent] Инициализация для чата ${widget.chatId}');
    }
  }

  @override
  void dispose() {
    // Помечаем, что компонент уничтожается
    _isAppInBackground = true;

    // Отменяем и очищаем таймер
    _inactivityTimer?.cancel();
    _inactivityTimer = null;

    // Удаляем observer
    WidgetsBinding.instance.removeObserver(this);

    // Очищаем контроллеры
    _scrollController.dispose();
    _textController.dispose();

    super.dispose();
  }

  @override
  void didUpdateWidget(AIChatContent oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.chatId != widget.chatId) {
      if (kDebugMode) {
        debugPrint(
          '🔄 [AIChatContent] Обновление с чата ${oldWidget.chatId} на чат ${widget.chatId}',
        );
      }
      _resetInactivityTimer();
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        _isAppInBackground = false;
        _resetInactivityTimer();
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        _isAppInBackground = true;
        _inactivityTimer?.cancel();
        break;
      case AppLifecycleState.detached:
        _isAppInBackground = true;
        _inactivityTimer?.cancel();
        break;
      case AppLifecycleState.hidden:
        _isAppInBackground = true;
        _inactivityTimer?.cancel();
        break;
    }
  }

  void _resetInactivityTimer() {
    // Отменяем предыдущий таймер
    _inactivityTimer?.cancel();
    _inactivityTimer = null;

    // Не запускаем новый таймер если приложение в фоне или компонент уничтожен
    if (_isAppInBackground || !mounted) return;

    _inactivityTimer = Timer(const Duration(minutes: 30), () {
      // Дополнительная проверка на актуальность состояния
      if (!_isAppInBackground && mounted) {
        if (kDebugMode) {
          debugPrint(
            '🔄 [AIChatScreen] Сброс состояния чата из-за неактивности',
          );
        }
        try {
          ref.read(aiChatProvider(widget.chatId).notifier).resetChatState();
        } catch (e) {
          if (kDebugMode) {
            debugPrint('❌ [AIChatScreen] Ошибка при сбросе состояния чата: $e');
          }
        }
      }
    });
  }

  void _handleStateChanges(BuildContext context, AIChatState state) {
    // Проверяем, что компонент все еще активен
    if (!mounted) return;

    // Обработка ошибок
    if (state.hasError && !_lastErrorShown) {
      _lastErrorShown = true;

      if (kDebugMode) {
        debugPrint('❌ [AIChatScreen] Ошибка: ${state.error}');
      }

      // Показываем ошибку пользователю
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(state.error!),
            backgroundColor: Theme.of(context).colorScheme.error,
            action: SnackBarAction(
              label: 'Повторить',
              textColor: Theme.of(context).colorScheme.onError,
              onPressed: () {
                // Очищаем ошибку и пытаемся снова
                ref.read(aiChatProvider(widget.chatId).notifier).clearError();
                ref.read(aiChatProvider(widget.chatId).notifier).loadMessages();
              },
            ),
            duration: const Duration(seconds: 10),
          ),
        );
      }
    } else if (!state.hasError && _lastErrorShown) {
      // Сбрасываем флаг ошибки когда ошибка исчезла
      _lastErrorShown = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Обрабатываем изменения состояния
    ref.listen<AIChatState>(
      aiChatProvider(widget.chatId),
      (previous, next) => _handleStateChanges(context, next),
    );

    return Consumer(
      builder: (context, ref, child) {
        final chatState = ref.watch(aiChatProvider(widget.chatId));
        final authState = ref.watch(authProvider);

        // Если пользователь не авторизован
        if (!authState.isAuthenticated) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.lock_outline, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'Для использования ИИ-ассистента\nнеобходимо войти в систему',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
                SizedBox(height: 16),
                Text(
                  'Перейдите в настройки для входа',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ],
            ),
          );
        }

        // Если идет загрузка авторизации
        if (authState.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        // Показываем индикатор загрузки при первоначальной загрузке
        if (chatState.isLoading && chatState.messages.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  'Загрузка чата...',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          );
        }

        // Получаем сообщения из состояния
        final messages = chatState.messages;
        final isMessageSending = chatState.isMessageSending;
        final streamingMessage = chatState.streamingMessage;

        // Основной интерфейс чата
        return Column(
          children: [
            // Основной контент чата
            Expanded(
              child: ChatLayout(
                messages: messages,
                scrollController: _scrollController,
                inputBuilder:
                    () => MessageInputWidget(
                      controller: _textController,
                      onSubmitted:
                          (text, attachments) => _sendMessage(
                            content: text,
                            attachments:
                                attachments
                                    .map(
                                      (a) => AIAttachment(
                                        id: 0,
                                        messageId: 0,
                                        type: a.type,
                                        url: a.url,
                                        filename: a.filename,
                                        size: a.size,
                                        mimeType: a.mimeType,
                                        createdAt: DateTime.now(),
                                        localBytes: a.localBytes,
                                      ),
                                    )
                                    .toList(),
                          ),
                    ),
                isMessageSending: isMessageSending,
                streamingMessage: streamingMessage,
              ),
            ),
          ],
        );
      },
    );
  }

  /// Отправляет сообщение
  Future<void> _sendMessage({
    required String content,
    List<AIAttachment>? attachments,
  }) async {
    if (kDebugMode) {
      debugPrint('📤 [AIChatScreen] Отправка сообщения: $content');
      if (attachments != null && attachments.isNotEmpty) {
        for (int i = 0; i < attachments.length; i++) {
          final attachment = attachments[i];
          debugPrint(
            '📎 [AIChatScreen] Вложение $i: ${attachment.filename}, localBytes: ${attachment.localBytes != null ? "есть" : "нет"}',
          );
        }
      }
    }

    // Очищаем поле ввода сразу
    _textController.clear();

    // Сбрасываем таймер неактивности
    _resetInactivityTimer();

    try {
      await ref
          .read(aiChatProvider(widget.chatId).notifier)
          .sendMessage(content: content, attachments: attachments);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ [AIChatScreen] Ошибка отправки сообщения: $e');
      }

      // Показываем ошибку пользователю
      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка отправки сообщения: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
            action: SnackBarAction(
              label: 'Повторить',
              textColor: Theme.of(context).colorScheme.onError,
              onPressed:
                  () =>
                      _sendMessage(content: content, attachments: attachments),
            ),
          ),
        );
      }
    }
  }
}
