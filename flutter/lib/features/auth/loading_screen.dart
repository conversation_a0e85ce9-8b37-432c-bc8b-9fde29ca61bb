import 'package:flutter/material.dart';
import 'package:luxury_app/features/drawer/app_logo.dart';

/// Экран загрузки, отображаемый во время проверки авторизации
class LoadingScreen extends StatelessWidget {
  const LoadingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Логотип приложения
            const AppLogo(),

            const SizedBox(height: 48),

            // Индикатор загрузки
            SizedBox(
              width: 32,
              height: 32,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.primary,
                ),
              ),
            ),

            const Si<PERSON><PERSON><PERSON>(height: 24),

            // Текст загрузки
            Text(
              'Проверка авторизации...',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
